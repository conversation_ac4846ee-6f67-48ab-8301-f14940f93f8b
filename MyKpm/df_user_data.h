//
// Created by Administrator on 2025/5/13.
//

#ifndef __DF_USER_DATA_H__
#define __DF_USER_DATA_H__

#include <stdint.h>

//ssize_t recvfrom(int sockfd, void *buf, size_t len, int flags, struct sockaddr *src_addr, socklen_t *addrlen);
#define CUSTOM_SYSCALL_NO 207
#define ACTION_BASE 0x7FFFFF00
#define ACTION_SUBMIT_DATA ACTION_BASE
#define ACTION_DELETE_DATA ACTION_SUBMIT_DATA + 1
#define ACTION_SUBMIT_HOOK_DATA ACTION_DELETE_DATA + 1

struct user_data{
    int uid;
    char pkg[128];
    int64_t install_time_sec;

    int64_t total_running_time;
    int64_t total_sleep_time;

    int64_t data_app_sec;
    long data_app_nsec;

    int64_t data_cache_sec;
    long data_cache_nsec;

    int64_t data_dalvik_cache_sec;
    long data_dalvik_cache_nsec;

    int64_t data_data_sec;
    long data_data_nsec;

    int64_t data_user_sec;
    long data_user_nsec;

    int64_t data_nfc_sec;
    long data_nfc_nsec;

    int64_t data_system_sec;
    long data_system_nsec;

    int64_t data_vendor_sec;
    long data_vendor_nsec;

    int64_t storage_self_primary_sec;
    long storage_self_primary_nsec;

    int64_t mnt_sec;
    long mnt_nsec;

    int64_t storage_sec;
    long storage_nsec;

    int stat_random1;
    int stat_random2;

    int stat_random3;
    int stat_random4;

    int64_t data_sec;
    long data_nsec;

};

#endif //__DF_USER_DATA_H__
