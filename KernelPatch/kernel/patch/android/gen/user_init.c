static const char user_init[] = "#!/bin/sh\n\nKPMS_DIR=\"/data/adb/ap/kpms/\"\nMAGISK_POLICY_PATH=\"/data/adb/ap/bin/magiskpolicy\"\nSUPERCMD=\"truncate\"\nMAGISK_SCTX=\"u:r:magisk:s0\"\nAPD_PATH=\"/data/adb/apd\"\nDEV_LOG_DIR=\"/dev/user_init_log/\"\n\nskey=\"$1\"\nevent=\"$2\"\n\nmkdir -p \"$DEV_LOG_DIR\"\n\nLOG_FILE=\"$DEV_LOG_DIR\"\"$event\"\n\nexec >>$LOG_FILE 2>&1\n\nset -x\n\nload_modules() {\n    for dir in \"$KPMS_DIR/*\"; do\n        if [ ! -d \"$dir\" ]; then continue; fi\n        if [ -e \"$dir/disable\" ]; then continue; fi\n        main_sh=\"$dir/main.sh\"\n        if [ -e \"$main_sh\" ]; then\n            touch \"$dir/disable\"\n            echo \"loading $dir/main.sh ...\"\n            . \"$main_sh\"\n            rm -f \"$dir/disable\"\n        else\n            echo \"Error: $main_sh not found in $dir\"\n        fi\n    done\n}\n\nhandle() {\n    $SUPERCMD $skey event $event \"before\"\n    case \"$event\" in\n    \"early-init\" | \"init\" | \"late-init\") ;;\n    \"post-fs-data\")\n        $MAGISK_POLICY_PATH --magisk --live\n        load_modules $skey $event\n        $SUPERCMD $skey -Z $MAGISK_SCTX exec $APD_PATH -s $skey $event\n        ;;\n    \"services\")\n        $SUPERCMD $skey -Z $MAGISK_SCTX exec $APD_PATH -s $skey $event\n        ;;\n    \"boot-completed\")\n        $SUPERCMD $skey -Z $MAGISK_SCTX exec $APD_PATH -s $skey $event\n        $SUPERCMD su -Z $MAGISK_SCTX exec $APD_PATH uid-listener &\n        ;;\n    *)\n        echo \"unknown user_init event: $event\"\n        ;;\n    esac\n    $SUPERCMD $skey event $event \"after\"\n}\n\nhandle\n";
