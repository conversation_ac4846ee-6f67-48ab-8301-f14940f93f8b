#ifndef __DF_COMMON_H__
#define __DF_COMMON_H__

#include "df_header.h"
#include "df_data_type.h"


int df_common_init(const char *args, const char *event, void *__user reserved);
int df_common_exit(void *__user reserved);

//process and thread
char *df__get_task_comm(char *buf, size_t buf_size, struct task_struct *tsk);
pid_t df_current_pid();

//file operators
int df_do_sys_open(int dfd, const char __user *filename, int flags, umode_t mode);
int df_get_unused_fd_flags(unsigned flags);
void df_fd_install(unsigned int fd, struct file *file);
struct file* df_filp_open(const char *filename, int flags, umode_t mode);
int df_filp_close(struct file *filp, fl_owner_t id);


//print stack
void df_dump_stack(void);



//memory
long df_copy_from_user(void *dst, const void __user *src, size_t size);
void df_vfree(const void *addr);
void *df_vmalloc(unsigned long size);
unsigned long df__get_free_pages(gfp_t gfp_mask, unsigned int order);
void df_free_pages(unsigned long addr, unsigned int order);

//time
void df_ktime_get_real_ts64(struct timespec64 *ts);

//strings
bool df_starts_with(const char *str, const char *prefix);

//dentry 
char *df_dentry_path(struct dentry *dentry, char *buf, int buflen);
char *df_d_path(const struct path *, char *, int);


void df_down_read(struct rw_semaphore *sem);
void df_up_read(struct rw_semaphore *sem);
struct mm_struct *df_get_task_mm(struct task_struct *task);
void df_mmput(struct mm_struct *);

//fd
bool df_get_path_from_fd(int fd, char *outbuf, size_t buflen);
int df___arm64_sys_close(int fd);
void df_put_unused_fd(unsigned int fd);
void df_fput(struct file * file);

#endif
