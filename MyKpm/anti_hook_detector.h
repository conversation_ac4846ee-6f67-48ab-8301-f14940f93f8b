#ifndef _ANTI_HOOK_DETECTOR_H
#define _ANTI_HOOK_DETECTOR_H

#include "df_common.h"
#include "df_user_data.h"

#define FUNC_PATCH_LEN 16

// 与用户态共享的数据结构
struct hook_info_data {
    unsigned int uid;
    unsigned int pid;
    void *func_addr;
    unsigned char original_code[FUNC_PATCH_LEN];
    unsigned char hooked_code[FUNC_PATCH_LEN];
    unsigned long page_addr;
    int enabled;
};

struct hook_entry {
    unsigned int uid;
    unsigned int pid;
    unsigned long func_addr;
    unsigned long page_addr;
    unsigned char original_code[FUNC_PATCH_LEN];
    unsigned char hooked_code[FUNC_PATCH_LEN];
    unsigned char current_code[FUNC_PATCH_LEN];  // 当前代码缓存
    unsigned long shadow_vaddr;                  // 影子页面虚拟地址（不使用struct page*）
    int enabled;                                 // 是否启用反hook检测
    int page_remapped;                          // 标记页面是否已重映射
    unsigned long backup_data[4];               // 备份数据，用于恢复
};
// 函数声明

/**
 * 处理用户态提交的hook信息
 * @param hook_info: 用户态提交的hook信息
 * @return: 0成功，负数失败
 */
int process_hook_info(const struct hook_info_data *hook_info);

/**
 * 清理指定uid的所有hook信息
 * @param uid: 用户ID
 * @return: 0成功，负数失败
 */
int cleanup_hooks_by_uid(unsigned int uid);

/**
 * 页面错误处理函数
 * @param regs: 寄存器状态
 * @param addr: 错误地址
 * @param esr: 异常状态寄存器
 * @return: 0处理成功，负数未处理
 */
int anti_hook_page_fault_handler(struct pt_regs *regs, unsigned long addr, unsigned int esr);

/**
 * 模块初始化
 * @return: 0成功，负数失败
 */
int anti_hook_init(void);

/**
 * 模块清理
 */
void anti_hook_cleanup(void);

/**
 * 启用指定进程的反hook检测
 * @param uid: 用户ID
 * @param pid: 进程ID
 * @param func_addr: 函数地址
 * @return: 0成功，负数失败
 */
int enable_anti_hook_for_process(unsigned int uid, unsigned int pid, unsigned long func_addr);

/**
 * 禁用指定进程的反hook检测
 * @param uid: 用户ID
 * @param pid: 进程ID
 * @param func_addr: 函数地址
 * @return: 0成功，负数失败
 */
int disable_anti_hook_for_process(unsigned int uid, unsigned int pid, unsigned long func_addr);

/**
 * 获取hook统计信息
 * @param uid: 用户ID，0表示所有用户
 * @return: hook数量
 */
int get_hook_count(unsigned int uid);

/**
 * 调试函数：打印所有hook信息
 */
void dump_all_hooks(void);

/**
 * 核心功能：模拟内存读取，返回原始代码
 * @param addr: 要读取的地址
 * @param buffer: 输出缓冲区
 * @param size: 读取大小
 * @return: 0成功，负数失败
 */
int anti_hook_memory_read(unsigned long addr, void *buffer, size_t size);

/**
 * 检查指定地址是否被hook并且启用了反检测
 * @param addr: 要检查的地址
 * @return: 1表示被hook且启用反检测，0表示没有
 */
int is_address_anti_hooked(unsigned long addr);

/**
 * 临时恢复hook代码（用于实际执行）
 * @param uid: 用户ID
 * @param func_addr: 函数地址
 * @return: 0成功，负数失败
 */
int restore_hook_temporarily(unsigned int uid, unsigned long func_addr);

/**
 * 重新应用原始代码（用于绕过检测）
 * @param uid: 用户ID
 * @param func_addr: 函数地址
 * @return: 0成功，负数失败
 */
int apply_original_code(unsigned int uid, unsigned long func_addr);

#endif /* _ANTI_HOOK_DETECTOR_H */