{"files.associations": {"hook.h": "c", "syscall.h": "c", "current.h": "c", "kputils.h": "c", "string.h": "c", "capability.h": "c", "cred.h": "c", "df_ipc.h": "c", "df_common.h": "c", "unistd.h": "c", "compiler.h": "c", "df_core.h": "c", "df_header.h": "c", "uaccess.h": "c", "printk.h": "c", "kpmodule.h": "c", "err.h": "c", "kernel.h": "c", "stat.h": "c", "df_user_data.h": "c", "df_data_type.h": "c", "list.h": "c", "gfp.h": "c", "common.h": "c", "slab.h": "c", "df_constants.h": "c", "stdint.h": "c", "fs.h": "c", "sched.h": "c", "llist.h": "c", "anti_hook.h": "c", "anti_hook_detector.h": "c"}}