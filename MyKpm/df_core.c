#include "df_core.h"
#include "df_common.h"
#include "df_data_type.h"
#include "df_ipc.h"
#include "df_constants.h"


enum hook_type openat_hook_type = NONE;
enum hook_type readlinkat_hook_type = NONE;
enum hook_type fstatfs_hook_type = NONE;
enum hook_type fstat_hook_type = NONE;
enum hook_type clock_gettime_hook_type = NONE;
enum hook_type faccessat_hook_type = NONE;
 
enum hook_type vfs_statx_hook_type = NONE;
enum hook_type arch_setup_additional_pages_type = NONE;
enum hook_type __vdso_init_type = NONE;
enum hook_type __setup_additional_pages_type = NONE;
enum hook_type show_vfsmnt_type = NONE;
enum hook_type show_mountinfo_type = NONE;
enum hook_type show_vfsstat_type = NONE;
enum hook_type show_map_vma_type = NONE;
enum hook_type proc_fill_cache_type = NONE;

//fun ptrs
struct linux_binprm;
struct mm_struct;
enum vdso_abi;
struct seq_file;


int (*vfs_statx_addr)(int dfd, const char __user *filename, int flags,struct kstat *stat, u32 request_mask) = 0;
int (*arch_setup_additional_pages_addr)(struct linux_binprm *bprm, int uses_interp) = 0;
int (*__vdso_init_addr)(enum vdso_abi abi) = 0;
int (*__setup_additional_pages_addr)(enum vdso_abi abi, struct mm_struct *mm, struct linux_binprm *bprm, int uses_interp) = 0;
int (*show_vfsmnt_addr)(struct seq_file *m, struct vfsmount *mnt) = 0;
int (*show_mountinfo_addr)(struct seq_file *m, struct vfsmount *mnt) = 0;
int (*show_vfsstat_addr)(struct seq_file *m, struct vfsmount *mnt) = 0;
void (*show_map_vma_addr)(struct seq_file *m, struct vm_area_struct *vma) = 0;


typedef struct dentry *instantiate_t(struct dentry *,
				     struct task_struct *, const void *);
bool (*proc_fill_cache_addr)(struct file *file, struct dir_context *ctx, 
    const char *name, unsigned int len,
	instantiate_t instantiate, struct task_struct *task, const void *ptr) = 0;




static void df_before_openat(hook_fargs4_t *args, void *udata)
{
    uid_t uid = current_uid();
    struct user_data* pUserData = df_query_user_data(uid);
    if(!pUserData){
        return;
    }

    int dfd = (int)syscall_argn(args, 0);
    const char __user *filename = (typeof(filename))syscall_argn(args, 1);
    int flag = (int)syscall_argn(args, 2);
    umode_t mode = (int)syscall_argn(args, 3);

    char buf[BUFF_LEN_512];
    memset(buf, 0, sizeof(buf));
    compat_strncpy_from_user(buf, filename, sizeof(buf));

    if(strstr(buf, BOOT_ID_FILE)){
        char comm[100];
        memset(comm, 0, sizeof(comm));
        if(df__get_task_comm(comm, sizeof(comm), current)){
            if(strstr(comm, "binder:")){
                return;
            }
        }
        
        char fakeBootIdFile[BUFF_LEN_256];
        memset(fakeBootIdFile, 0, sizeof(fakeBootIdFile));
        sprintf(fakeBootIdFile, "%s%d%s%s", DEVICE_INFO_DIR, uid, "_", REDIRECT_BOOT_ID_FILE_NAME);
        struct file *filp = df_filp_open(fakeBootIdFile, flag, mode);
        if (IS_ERR(filp)) {
            return;
        }

        //open success
        int rediretFd = -1;
        rediretFd = df_get_unused_fd_flags(0);
        if (rediretFd >= 0) {
            df_fd_install(rediretFd, filp);
            args->skip_origin = true;
            args->ret = rediretFd;
        } else {
            df_filp_close(filp, NULL);
        }
    }
    
}

static void df_after_readlinkat(hook_fargs4_t *args, void *udata)
{
    uid_t uid = current_uid();
    struct user_data* pUserData = df_query_user_data(uid);
    if(!pUserData){
        return;
    }

    int dfd = (int)syscall_argn(args, 0);
    const char __user *pathname = (typeof(pathname))syscall_argn(args, 1);
    char __user *buff = (typeof(buff))syscall_argn(args, 2);
    int buffSize = (int)syscall_argn(args, 3);
    int ret = args->ret;

    char kBuf[BUFF_LEN_1024];
    memset(kBuf, 0, sizeof(kBuf));
    if(ret < BUFF_LEN_1024){
        compat_strncpy_from_user(kBuf, buff, ret+1);
    }else{
        compat_strncpy_from_user(kBuf, buff, sizeof(kBuf));
    }

    
    int len = strlen(BOOT_ID_FILE);
    const char zero = 0;

    if(strstr(kBuf, DEVICE_INFO_DIR) && strstr(kBuf, REDIRECT_BOOT_ID_FILE_NAME)){
        compat_copy_to_user(buff, BOOT_ID_FILE,len);
        compat_copy_to_user(buff + len, &zero, 1);
    }
    
}

//unsigned int fd, struct statfs *buf
static void df_before_fstatfs(hook_fargs4_t *args, void *udata)
{
    uid_t uid = current_uid();
    struct user_data* pUserData = df_query_user_data(uid);
    if(!pUserData){
        return;
    }

    int fd = (int)syscall_argn(args, 0);
    if(fd <= 2){
        return;
    }

    char outBuff[BUFF_LEN_512];
    if(!df_get_path_from_fd(fd, outBuff, sizeof(outBuff))){
        return;
    }
    
    char redFile[BUFF_LEN_256];
    sprintf(redFile, "%s%d%s%s", DEVICE_INFO_DIR, uid, "_", REDIRECT_BOOT_ID_FILE_NAME);
    if(strstr(outBuff, redFile)){
        struct file *fp = df_filp_open(BOOT_ID_FILE, O_RDONLY, 0);
        if (!IS_ERR(fp)) {
            int newfd = df_get_unused_fd_flags(0);
            if (newfd >= 0) {
                df_fd_install(newfd, fp);
                set_syscall_argn(args, 0, newfd);
                args->local.data0 = uid;
                args->local.data1 = 100868788;
                args->local.data2 = (uint64_t)fp;
                args->local.data3 = newfd;
            } else {
                df_filp_close(fp, NULL);   
            }
        } else {
            logke("df_filp_open failed: %ld", PTR_ERR(fp));
        }
    }
}


static void df_after_fstatfs(hook_fargs2_t *args, void *udata)
{
    uid_t uid = current_uid();
    if(args->local.data0 == uid && args->local.data1 == 100868788){
        struct file *fp = (struct file *)args->local.data2;
        int newfd = (int)args->local.data3;
        df_put_unused_fd(newfd);
        df_fput(fp);
    }
}


//unsigned int fd, struct stat *statbuf
static void df_before_fstat(hook_fargs2_t *args, void *udata)
{
    uid_t uid = current_uid();
    struct user_data* pUserData = df_query_user_data(uid);
    if(!pUserData){
        return;
    }

    
    int fd = (int)syscall_argn(args, 0);
    if(fd <= 2){
        return;
    }

    char outBuff[BUFF_LEN_512];
    if(!df_get_path_from_fd(fd, outBuff, sizeof(outBuff))){
        return;
    }
    
    char redFile[BUFF_LEN_256];
    sprintf(redFile, "%s%d%s%s", DEVICE_INFO_DIR, uid, "_", REDIRECT_BOOT_ID_FILE_NAME);
    if(strstr(outBuff, redFile)){
        struct file *fp = df_filp_open(BOOT_ID_FILE, O_RDONLY, 0);
        if (!IS_ERR(fp)) {
            int newfd = df_get_unused_fd_flags(0);
            if (newfd >= 0) {
                df_fd_install(newfd, fp);
                set_syscall_argn(args, 0, newfd);
                args->local.data0 = uid;
                args->local.data1 = 100868788;
                args->local.data2 = (uint64_t)fp;
                args->local.data3 = newfd;
            } else {
                df_filp_close(fp, NULL);   
            }
        } else {
            logke("df_filp_open failed: %ld", PTR_ERR(fp));
        }
    }
}

static void df_after_fstat(hook_fargs2_t *args, void *udata)
{
    uid_t uid = current_uid();
    if(args->local.data0 == uid && args->local.data1 == 100868788){
        struct file *fp = (struct file *)args->local.data2;
        int newfd = (int)args->local.data3;
        df_put_unused_fd(newfd);
        df_fput(fp);
    }
}


    //const clockid_t which_clock, struct __kernel_timespec *tp
static int mLastCpuTime = 0;    
static void df_after_clock_gettime(hook_fargs2_t *args, void *udata)
{
    uid_t uid = current_uid();
    struct user_data* pUserData = df_query_user_data(uid);
    if(pUserData){
        clockid_t which_clock = (clockid_t)syscall_argn(args, 0);
        if(which_clock == CLOCK_MONOTONIC || which_clock == CLOCK_MONOTONIC_RAW 
                || which_clock == CLOCK_MONOTONIC_COARSE || which_clock == CLOCK_BOOTTIME){
   
            struct __kernel_timespec* pUserTspec = (struct __kernel_timespec*)syscall_argn(args, 1);
            struct __kernel_timespec kTspec;
            memset(&kTspec, 0, sizeof(kTspec));
            df_copy_from_user(&kTspec, pUserTspec, sizeof(kTspec));
           
            if(which_clock == CLOCK_BOOTTIME){
                kTspec.tv_sec += pUserData->total_running_time;
            }else{
                  kTspec.tv_sec += (pUserData->total_running_time - pUserData->total_sleep_time);
            }
          
            compat_copy_to_user(pUserTspec, &kTspec, sizeof(kTspec));
        }
        
    }
}
//SYSCALL_DEFINE3(faccessat, int, dfd, const char __user *, filename, int, mode)
static void df_before_faccessat(hook_fargs3_t *args, void *udata)
{
    uid_t uid = current_uid();
    if(uid <= APP_BASE_UID){
        return;
    }

    int dfd = (int)syscall_argn(args, 0);
    const char* filename = (const char *)syscall_argn(args, 1);
    char kBuff[BUFF_LEN_1024];
    // memset(kBuff, 0, sizeof(kBuff));
    compat_strncpy_from_user(kBuff, filename, sizeof(kBuff));
    if(!strcmp(kBuff, HIDE_ADBD)){
        args->ret = -ENOENT;
        args->skip_origin = true;
    }
}


static void updateFileTime(struct kstat* pKstat, int64_t second, long nsecond, int secOffset, int nsecOffset)
{
        pKstat->mtime.tv_sec = second;
        pKstat->mtime.tv_nsec = nsecond;

        pKstat->ctime.tv_sec = second;
        pKstat->ctime.tv_nsec = nsecond;

        pKstat->atime.tv_sec = second - secOffset;
        pKstat->atime.tv_nsec = nsecond - nsecOffset;

        if(pKstat->atime.tv_sec <= 0){
            pKstat->atime.tv_sec = second;
            logke("updateFileTime error: atime.tv_sec <= 0");
        }

        if(pKstat->atime.tv_nsec <= 0){
            pKstat->atime.tv_nsec = nsecond;
            logke("updateFileTime error: atime.tv_nsec <= 0");
        }
}

static uid_t lastUid = 0;
static void df_after_vfs_statx(hook_fargs5_t *args, void *udata)
{
    
    uid_t uid = current_uid();
    struct user_data* pUserData = NULL;

    if(uid == UID_SHELL){
        if(lastUid != 0){
            pUserData = df_query_user_data(lastUid);
        }
    }else{
        pUserData = df_query_user_data(uid);
        if(pUserData){
            lastUid = uid;
        }
    }

    if(pUserData == NULL){//如果没获取到数据，说明当前用户不需要改机
        return;
    }
    

    //static int vfs_statx(int dfd, const char __user *filename, int flags, struct kstat *stat, u32 request_mask)
     int dfd = (int)args->arg0;
    const char __user *filename = (const char*)args->arg1;
    int flags = (int)args->arg2;
    struct kstat * pKstat = (struct kstat *)args->arg3;

    char kFileNameBuff[BUFF_LEN_512];
    memset(kFileNameBuff, 0, sizeof(kFileNameBuff));
    compat_strncpy_from_user(kFileNameBuff, filename, sizeof(kFileNameBuff));
    
    
    if(df_starts_with(kFileNameBuff, PATH_DATA_APP) && strstr(kFileNameBuff, pUserData->pkg)){
        //Access: 2025-05-22 18:38:09.599009865 +0800
        //Modify: 2025-05-22 18:38:18.211009865 +0800
        //Change: 2025-05-22 18:38:18.211009865 +0800
        //apk install path
        int num = pUserData->stat_random1 % 19;
        pKstat->atime.tv_sec = pUserData->install_time_sec - num - 2;
        pKstat->atime.tv_nsec = pUserData->stat_random1;
        
    
        pKstat->mtime.tv_sec = pUserData->install_time_sec;
        pKstat->mtime.tv_nsec = pUserData->stat_random1 + pUserData->stat_random2;

        pKstat->ctime.tv_sec = pKstat->mtime.tv_sec;
        pKstat->ctime.tv_nsec = pKstat->mtime.tv_nsec;
    }
    
    else if (df_starts_with(kFileNameBuff, PATH_DATA_APP))
    {
        updateFileTime(pKstat, pUserData->data_app_sec, pUserData->data_app_nsec, pUserData->stat_random1, pUserData->stat_random4);
    }else if (df_starts_with(kFileNameBuff, PATH_DATA_CACHE))
    {
        updateFileTime(pKstat, pUserData->data_cache_sec, pUserData->data_cache_nsec, pUserData->stat_random1, pUserData->stat_random4);
    }else if (df_starts_with(kFileNameBuff, PATH_DATA_DALVIK_CACHE))
    {
        updateFileTime(pKstat, pUserData->data_dalvik_cache_sec, pUserData->data_dalvik_cache_nsec, pUserData->stat_random1, pUserData->stat_random4);
    }else if (df_starts_with(kFileNameBuff, PATH_DATA_DATA))
    {
        updateFileTime(pKstat, pUserData->data_data_sec, pUserData->data_data_nsec, pUserData->stat_random2, pUserData->stat_random4);
    }else if (df_starts_with(kFileNameBuff, PATH_DATA_USER))
    {
        //data/user.act_time == /data/data.a_time
        updateFileTime(pKstat, pUserData->data_data_sec, pUserData->data_data_nsec, pUserData->stat_random2, pUserData->stat_random4);
        pKstat->ctime.tv_sec = pKstat->atime.tv_sec;
        pKstat->ctime.tv_nsec = pKstat->atime.tv_nsec;
        pKstat->mtime.tv_sec = pKstat->atime.tv_sec;
        pKstat->mtime.tv_nsec = pKstat->atime.tv_nsec;

    }
    
    
    
    else if (df_starts_with(kFileNameBuff, PATH_DATA_NFC))//second group
    {
        updateFileTime(pKstat, pUserData->data_nfc_sec, pUserData->data_nfc_nsec, pUserData->stat_random2, pUserData->stat_random4);
    }else if (df_starts_with(kFileNameBuff, PATH_DATA_SYSTEM))
    {
        updateFileTime(pKstat, pUserData->data_system_sec, pUserData->data_system_nsec, pUserData->stat_random2, pUserData->stat_random4);
    }else if (df_starts_with(kFileNameBuff, PATH_DATA_VENDOR))
    {
        updateFileTime(pKstat, pUserData->data_vendor_sec, pUserData->data_vendor_nsec, pUserData->stat_random3, pUserData->stat_random4);
    }else if (df_starts_with(kFileNameBuff, PATH_DATA))
    {
        updateFileTime(pKstat, pUserData->data_sec, pUserData->data_nsec, pUserData->stat_random3, pUserData->stat_random4);
    }
    else if (df_starts_with(kFileNameBuff, PATH_STORAGE_SELF_PRIMARY))
    {
        updateFileTime(pKstat, pUserData->storage_self_primary_sec, pUserData->storage_self_primary_nsec, pUserData->stat_random3, pUserData->stat_random4);
    }else if (df_starts_with(kFileNameBuff, PATH_MNT))
    {
        updateFileTime(pKstat, pUserData->mnt_sec, pUserData->mnt_nsec, pUserData->stat_random3, pUserData->stat_random4);
    }
    

    else if (df_starts_with(kFileNameBuff, PATH_STORAGE))//last group
    {
        updateFileTime(pKstat, pUserData->storage_sec, pUserData->storage_nsec, pUserData->stat_random3, pUserData->stat_random4);
    }
    
}





//static int show_vfsmnt(struct seq_file *m, struct vfsmount *mnt)
static void df_before_show_vfsmnt(hook_fargs2_t *args, void *udata)
{
    uid_t uid = current_uid();
    if(uid <= APP_BASE_UID){
        return;
    }

    void* p_seq_file = (void*)args->arg0;
    struct vfsmount* mnt = (struct vfsmount*)args->arg1;
    struct mount *mnt_full = real_mount(mnt);
    const char *devname = mnt_full->mnt_devname;

    char pathBuf[BUFF_LEN_512];
    memset(pathBuf, 0, sizeof(pathBuf));
    char *pPath = df_dentry_path(mnt->mnt_root, pathBuf, sizeof(pathBuf));
    if (!IS_ERR(pPath) && (strstr(pPath, MAGISK_MODULE_PATH) || strstr(pPath, DEBUG_RAMDISK))){
        // logkd("call show_vfsmnt find path:%s", pPath);
        args->ret = 0;
        args->skip_origin = true;
        return;
    }

    if (!IS_ERR(devname) && df_starts_with(devname, APatchDeviceName)) {
        // logkd("call show_vfsmnt find device:%s", devname);
        args->ret = 0;
        args->skip_origin = true;
        return;
    }

}


//static int show_mountinfo(struct seq_file *m, struct vfsmount *mnt)
static void df_before_show_mountinfo(hook_fargs2_t *args, void *udata)
{
    uid_t uid = current_uid();
    if(uid <= APP_BASE_UID){
        return;
    }

    void* p_seq_file = (void*)args->arg0;
    struct vfsmount* mnt = (struct vfsmount*)args->arg1;
    struct mount *mnt_full = real_mount(mnt);
    const char *devname = mnt_full->mnt_devname;

    char pathBuf[BUFF_LEN_512];
    memset(pathBuf, 0, sizeof(pathBuf));
    char *pPath = df_dentry_path(mnt->mnt_root, pathBuf, sizeof(pathBuf));
    if (!IS_ERR(pPath) && (strstr(pPath, MAGISK_MODULE_PATH) || strstr(pPath, DEBUG_RAMDISK))){
        // logkd("call show_mountinfo find path:%s", pPath);
        args->ret = 0;
        args->skip_origin = true;
        return;
    }

    if (!IS_ERR(devname) && df_starts_with(devname, APatchDeviceName)) {
        // logkd("call show_mountinfo find device:%s", devname);
        args->ret = 0;
        args->skip_origin = true;
        return;
    }
 
}


//static int show_vfsstat(struct seq_file *m, struct vfsmount *mnt)
static void df_before_show_vfsstat(hook_fargs2_t *args, void *udata)
{
    uid_t uid = current_uid();
    if(uid <= APP_BASE_UID){
        return;
    }

    void* p_seq_file = (void*)args->arg0;
    struct vfsmount* mnt = (struct vfsmount*)args->arg1;
    struct mount *mnt_full = real_mount(mnt);
    const char *devname = mnt_full->mnt_devname;

    char pathBuf[BUFF_LEN_512];
    memset(pathBuf, 0, sizeof(pathBuf));
    char *pPath = df_dentry_path(mnt->mnt_root, pathBuf, sizeof(pathBuf));
    if (!IS_ERR(pPath) && (strstr(pPath, MAGISK_MODULE_PATH) || strstr(pPath, DEBUG_RAMDISK))){
        // logkd("call show_vfsstat find path:%s", pPath);
        args->ret = 0;
        args->skip_origin = true;
        return;
    }

    if (!IS_ERR(devname) && df_starts_with(devname, APatchDeviceName)) {
        // logkd("call show_vfsstat find device:%s", devname);
        args->ret = 0;
        args->skip_origin = true;
        return;
    }
}

//static void show_map_vma(struct seq_file *m, struct vm_area_struct *vma)
static void df_before_show_map_vma(hook_fargs2_t *args, void *udata)
{
    uid_t uid = current_uid();
    if(uid <= APP_BASE_UID){
        return;
    }

    void* m = (void*)args->arg0;
    struct vm_area_struct* vma = (struct vm_area_struct*)args->arg1;
    struct file *file = vma->vm_file;

    char pathBuff[BUFF_LEN_1024];
    memset(pathBuff, 0, sizeof(pathBuff));

    if (!file)
        return;

   
    char *path = df_d_path(&file->f_path, pathBuff, BUFF_LEN_1024);
    if (!IS_ERR(path)) {
        if (strstr(path, PAYLOAD_DIR)) {
            args->ret = 0;
            args->skip_origin = true;  // 跳过原函数，直接返回
        }
    }
}



//bool proc_fill_cache(struct file *, struct dir_context *, const char *, 
//  unsigned int, instantiate_t, struct task_struct *, const void *);
static void df_before_proc_fill_cache(hook_fargs6_t *args, void *udata)
{
    uid_t uid = current_uid();
    if(uid <= APP_BASE_UID){
        return;
    }

    const char *name = (const char *)args->arg2;
    int len = (int)(unsigned long)args->arg3;
    struct task_struct *task = (struct task_struct *)args->arg5;

    // 从 task 拿 mm，拿 VMA，然后拿 vm_file 路径
    struct mm_struct *mm = df_get_task_mm(task);
    if (!mm) return;

    df_down_read(&mm->mmap_lock);

    struct vm_area_struct *vma;
    char pathBuf[512];
    char temp[64];

    for (vma = mm->mmap; vma; vma = vma->vm_next) {
        struct file *vf = vma->vm_file;
        if (!vf)
            continue;

        memset(temp, 0, sizeof(temp));
        snprintf(temp, sizeof(temp), "%lx-%lx", vma->vm_start, vma->vm_end);
        if (strncmp(name, temp, len) != 0)
            continue;

    
        memset(pathBuf, 0, sizeof(pathBuf));
        char *path = df_d_path(&vf->f_path, pathBuf, sizeof(pathBuf));
        if (!IS_ERR(path) && strstr(path, PAYLOAD_DIR)) {
            args->ret = 0;
            args->skip_origin = true;
            df_up_read(&mm->mmap_lock);
            df_mmput(mm);
            return;
        }
    }

    df_up_read(&mm->mmap_lock);
    df_mmput(mm);
}



int df_core_init(const char *args, const char *event, void *__user reserved){
    hook_err_t err = HOOK_NO_ERR;
    
    err = fp_hook_syscalln(__NR_openat, 4, df_before_openat, 0, 0);
    if (err) {
        logke("hook __NR_openat error: %d\n", err);
    } else {
        logkd("hook __NR_openat success\n");
        openat_hook_type = FUNCTION_POINTER_CHAIN;
    }

    err = fp_hook_syscalln(__NR_readlinkat, 4, 0, df_after_readlinkat, 0);
    if (err) {
        logke("hook __NR_readlinkat error: %d\n", err);
    } else {
        logkd("hook __NR_readlinkat success\n");
        readlinkat_hook_type = FUNCTION_POINTER_CHAIN;
    }
    
    //__NR3264_fstatat
    err = fp_hook_syscalln(__NR3264_fstatfs, 2, df_before_fstatfs, df_after_fstatfs, 0);
    if (err) {
        logke("hook __NR3264_fstatfs error: %d\n", err);
    } else {
        logkd("hook __NR3264_fstatfs success\n");
        fstatfs_hook_type = FUNCTION_POINTER_CHAIN;
    }

    err = fp_hook_syscalln(__NR3264_fstat, 2, df_before_fstat, df_after_fstat, 0);
    if (err) {
        logke("hook __NR3264_fstat error: %d\n", err);
    } else {
        logkd("hook __NR3264_fstat success\n");
        fstat_hook_type = FUNCTION_POINTER_CHAIN;
    }


    err = fp_hook_syscalln(__NR_clock_gettime, 2, 0, df_after_clock_gettime, 0);
    if (err) {
        logke("hook __NR_clock_gettime error: %d\n", err);
    } else {
        logkd("hook __NR_clock_gettime success\n");
        clock_gettime_hook_type = FUNCTION_POINTER_CHAIN;
    }

    err = fp_hook_syscalln(__NR_faccessat, 3, df_before_faccessat, 0, 0);
    if (err) {
        logke("hook __NR_faccessat error: %d\n", err);
    } else {
        logkd("hook __NR_faccessat success\n");
        faccessat_hook_type = FUNCTION_POINTER_CHAIN;
    }


    vfs_statx_addr = (typeof(vfs_statx_addr))kallsyms_lookup_name("vfs_statx");
    if(vfs_statx_addr){
        hook_err_t err = hook_wrap5((void*)vfs_statx_addr, 0, df_after_vfs_statx, 0);
        if(err){
            logke("hook vfs_statx failed");
        }else{
            logkd("hook vfs_statx success");
            vfs_statx_hook_type = INLINE_CHAIN;
        }
    }
    
    show_vfsmnt_addr = (typeof(show_vfsmnt_addr))kallsyms_lookup_name("show_vfsmnt");
    if(show_vfsmnt_addr){
        hook_err_t err = hook_wrap2((void*)show_vfsmnt_addr, df_before_show_vfsmnt, 0, 0);
        if(err){
            logke("hook show_vfsmnt failed");
        }else{
            logkd("hook show_vfsmnt success");
            show_vfsmnt_type = INLINE_CHAIN;
        }
    }

    show_mountinfo_addr = (typeof(show_mountinfo_addr))kallsyms_lookup_name("show_mountinfo");
    if(show_mountinfo_addr){
        hook_err_t err = hook_wrap2((void*)show_mountinfo_addr, df_before_show_mountinfo, 0, 0);
        if(err){
            logke("hook show_mountinfo failed");
        }else{
            logkd("hook show_mountinfo success");
            show_mountinfo_type = INLINE_CHAIN;
        }
    }

    show_vfsstat_addr = (typeof(show_vfsstat_addr))kallsyms_lookup_name("show_vfsstat");
    if(show_vfsstat_addr){
        hook_err_t err = hook_wrap2((void*)show_vfsstat_addr, df_before_show_vfsstat, 0, 0);
        if(err){
            logke("hook show_vfsstat failed");
        }else{
            logkd("hook show_vfsstat success");
            show_vfsstat_type = INLINE_CHAIN;
        }
    }
    
    show_map_vma_addr = (typeof(show_map_vma_addr))kallsyms_lookup_name("show_map_vma");
    if(show_map_vma_addr){
        hook_err_t err = hook_wrap2((void*)show_map_vma_addr, df_before_show_map_vma, 0, 0);
        if(err){
            logke("hook show_map_vma failed");
        }else{
            logkd("hook show_map_vma success");
            show_map_vma_type = INLINE_CHAIN;
        }
    }

   

    proc_fill_cache_addr = (typeof(proc_fill_cache_addr))kallsyms_lookup_name("proc_fill_cache");
    if(proc_fill_cache_addr){
        hook_err_t err = hook_wrap7((void*)proc_fill_cache_addr, df_before_proc_fill_cache, 0, 0);
        if(err){
            logke("hook proc_fill_cache failed");
        }else{
            logkd("hook proc_fill_cache success");
            proc_fill_cache_type = INLINE_CHAIN;
        }
    }


    logkd("kernel verison:%d, kernelpatch version:%d", kver, kpver);
    return 0;
}

int df_core_exit(void *__user reserved)
{
    if(openat_hook_type == FUNCTION_POINTER_CHAIN){
        fp_unhook_syscalln(__NR_openat, df_before_openat, 0);
        logkd("unhook __NR_openat\n");
    }

    if(readlinkat_hook_type == FUNCTION_POINTER_CHAIN){
        fp_unhook_syscalln(__NR_readlinkat, 0, df_after_readlinkat);
        logkd("unhook __NR_readlinkat\n");
    }

    if(fstatfs_hook_type == FUNCTION_POINTER_CHAIN){
        fp_unhook_syscalln(__NR3264_fstatfs, df_before_fstatfs, df_after_fstatfs);
        logkd("unhook __NR3264_fstatfs\n");
    }

    if(fstat_hook_type == FUNCTION_POINTER_CHAIN){
        fp_unhook_syscalln(__NR3264_fstat, df_before_fstat, df_after_fstat);
        logkd("unhook __NR3264_fstat\n");
    }


    if(clock_gettime_hook_type == FUNCTION_POINTER_CHAIN){
        fp_unhook_syscalln(__NR_clock_gettime, 0, df_after_clock_gettime);
        logkd("unhook __NR_clock_gettime\n");
    }

     if(faccessat_hook_type == FUNCTION_POINTER_CHAIN){
        fp_unhook_syscalln(__NR_faccessat, df_before_faccessat, 0);
        logkd("unhook __NR_faccessat\n");
    }


    if(vfs_statx_hook_type == INLINE_CHAIN){
        unhook(vfs_statx_addr);
        logkd("unhook vfs_statx\n");
    }

    if(show_vfsmnt_type == INLINE_CHAIN){
        unhook(show_vfsmnt_addr);
        logkd("unhook show_vfsmnt\n");
    }

    if(show_mountinfo_type == INLINE_CHAIN){
        unhook(show_mountinfo_addr);
        logkd("unhook show_mountinfo\n");
    }

    if(show_vfsstat_type == INLINE_CHAIN){
        unhook(show_vfsstat_addr);
        logkd("unhook show_vfsstat\n");
    }

    if(show_map_vma_type== INLINE_CHAIN){
        unhook(show_map_vma_addr);
        logkd("unhook show_map_vma\n");
    }
    

    if(proc_fill_cache_type == INLINE_CHAIN){
        unhook(proc_fill_cache_addr);
        logkd("unhook proc_fill_cache\n");
    }
}