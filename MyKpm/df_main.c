/* SPDX-License-Identifier: GPL-2.0-or-later */
/* 
 * Copyright (C) 2023 bmax121. All Rights Reserved.
 */

#include "df_common.h"
#include "df_ipc.h"
#include "df_core.h"

KPM_NAME("DFKCore");
KPM_VERSION("1.0.0");
KPM_LICENSE("GPL v2");
KPM_AUTHOR("jerry");
KPM_DESCRIPTION("DF kernel core module");


static long df_kpm_init(const char *args, const char *event, void *__user reserved)
{
    logkd("MyKpm init");
    df_common_init(args, event, reserved);
    df_ipc_init(args, event, reserved);
    df_core_init(args, event, reserved);
    anti_hook_init();
    return 0;
}

static long df_kpm_control(const char *args, char *__user out_msg, int outlen)
{
    logkd("MyKpm control, args: %s\n", args);
    return 0;
}

static long df_kpm_exit(void *__user reserved)
{
    logkd("MyKpm exit ...\n");
    df_ipc_exit(reserved);
    df_core_exit(reserved);
    anti_hook_cleanup();
    return 0;
}

KPM_INIT(df_kpm_init);
KPM_CTL0(df_kpm_control);
KPM_EXIT(df_kpm_exit);
