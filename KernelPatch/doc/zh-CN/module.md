# KernelPatch Module

## 什么是 KernelPatch Module (KPM)

  **KPM is an ELF file that can be loaded and run within the kernel space by KernelPatch.**  

## How to write a KPM

  Here are a few examples that you can use to quickly understand.  

1. A simple hello world KPM: [hello-world](/kpm-demo/hello)  
2. How to do kernel function inline-hook via KPM: [inline-hook](/kpm-demo/inlinehook)  
3. How to hook system call via KPM: [syscallhook](/kpm-demo/syscallhook)  

### Working without Kernel source tree

### Working with Kernel soruce tree
