
static void df_before___setup_additional_pages(hook_fargs4_t *args, void *udata)
{
    
    int abi = (int)args->arg0;
    logkd("call df_before___setup_additional_pages abi=%d", abi);

}

//EINVAL		22	/* Invalid argument */
static void df_before___vdso_init(hook_fargs1_t *args, void *udata)
{
    //uid_t uid = current_uid();
    logkd("call df_before___vdso_init");
    df_dump_stack();
}

static void df_before_arch_setup_additional_pages(hook_fargs2_t *args, void *udata)
{
    // uid_t uid = current_uid();
    logkd("call df_before_arch_setup_additional_pages");
}



// arch_setup_additional_pages_addr = (typeof(arch_setup_additional_pages_addr))kallsyms_lookup_name("arch_setup_additional_pages");
    // if(arch_setup_additional_pages_addr){
    //     hook_err_t err = hook_wrap2((void*)arch_setup_additional_pages_addr, df_before_arch_setup_additional_pages, 0, 0);
    //     if(err){
    //         logke("hook arch_setup_additional_pages failed");
    //     }else{
    //         logkd("hook arch_setup_additional_pages success");
    //         arch_setup_additional_pages_type = INLINE_CHAIN;
    //     }
    // }

    // __vdso_init_addr = (typeof(__vdso_init_addr))kallsyms_lookup_name("__vdso_init");
    // if(__vdso_init_addr){
    //     hook_err_t err = hook_wrap1((void*)__vdso_init_addr, df_before___vdso_init, 0, 0);
    //     if(err){
    //         logke("hook __vdso_init failed");
    //     }else{
    //         logkd("hook __vdso_init success");
    //         __vdso_init_type = INLINE_CHAIN;
    //     }
    // }else{
    //     logke("__vdso_init not found");
    // }

    // __setup_additional_pages_addr = (typeof(__setup_additional_pages_addr))kallsyms_lookup_name("__setup_additional_pages");
    // if(__setup_additional_pages_addr){
    //     hook_err_t err = hook_wrap4((void*)__setup_additional_pages_addr, df_before___setup_additional_pages, 0, 0);
    //     if(err){
    //         logke("hook __setup_additional_pages failed");
    //     }else{
    //         logkd("hook __setup_additional_pages success");
    //         __setup_additional_pages_type = INLINE_CHAIN;
    //     }
    // }else{
    //     logke("__setup_additional_pages not found");
    // }




    // if(arch_setup_additional_pages_type == INLINE_CHAIN){
    //     unhook(arch_setup_additional_pages_addr);
    //     logkd("unhook arch_setup_additional_pages\n");
    // }

    // if(__vdso_init_type == INLINE_CHAIN){
    //     unhook(__vdso_init_addr);
    //     logkd("unhook __vdso_init\n");
    // }

    // if(__setup_additional_pages_type == INLINE_CHAIN){
    //     unhook(__setup_additional_pages_addr);
    //     logkd("unhook __setup_additional_pages\n");
    // }