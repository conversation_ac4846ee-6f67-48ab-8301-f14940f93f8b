TARGET_COMPILE = /home/<USER>/myworkspace/Apatch/arm-gnu-toolchain-12.2.rel1-x86_64-aarch64-none-elf/bin/aarch64-none-elf-

ifndef KP_DIR
    KP_DIR = ../KernelPatch
endif

CC = $(TARGET_COMPILE)gcc
LD = $(TARGET_COMPILE)ld
#CFLAGS += -fno-builtin -ffreestanding -nostdlib

INCLUDE_DIRS := . include patch/include linux/include linux/arch/arm64/include linux/tools/arch/arm64/include

INCLUDE_FLAGS := $(foreach dir,$(INCLUDE_DIRS),-I$(KP_DIR)/kernel/$(dir))

SRCS := df_main.c df_common.c df_ipc.c df_core.c anti_hook_detector.c
OBJS := $(patsubst %.c, out/%.o, $(SRCS))

OUT_DIR := out

all: $(OUT_DIR) $(OUT_DIR)/kcore.kpm

$(OUT_DIR):
	mkdir -p $(OUT_DIR)

$(OUT_DIR)/kcore.kpm: $(OBJS)
	$(CC) -r -o $@ $^

$(OUT_DIR)/%.o: %.c
	$(CC) $(CFLAGS) $(INCLUDE_FLAGS) -c -O2 -o $@ $<

.PHONY: clean
clean:
	rm -rf $(OUT_DIR)
