/* SPDX-License-Identifier: GPL-2.0-or-later */
/* 
 * Copyright (C) 2023 bmax121. All Rights Reserved.
 */

#include <linux/uaccess.h>
#include <linux/string.h>
#include <linux/printk.h>
#include <linux/cred.h>
#include <linux/fs.h>
#include <linux/err.h>
#include <linux/kernel.h>
#include <linux/list.h>
#include <linux/gfp.h>
#include <linux/slab.h>
#include <compiler.h>
#include <kpmodule.h>
#include <uapi/asm-generic/unistd.h>
#include <syscall.h>
#include <kputils.h>
#include <asm/current.h>
#include <hook.h>

KPM_NAME("MyDemo");
KPM_VERSION("1.0.0");
KPM_LICENSE("GPL v2");
KPM_AUTHOR("bob");
KPM_DESCRIPTION("Test Kpm Module");


enum hook_type hook_type = NONE;
void before_openat_1(hook_fargs4_t *args, void *udata)
{
    logkd("call before_openat");
}

static long syscall_hook_demo_init(const char *args, const char *event, void *__user reserved)
{
    hook_err_t err = HOOK_NO_ERR;
    err = fp_hook_syscalln(__NR_openat, 4, before_openat_1, 0, 0);
    if (err) {
        logkd("hook __NR_openat error: %d\n", err);
    } else {
        logkd("hook __NR_openat success\n");
        hook_type = FUNCTION_POINTER_CHAIN;
    }
    return 0;
}

static long syscall_hook_control0(const char *args, char *__user out_msg, int outlen)
{
    logkd("syscall_hook control, args: %s\n", args);
    return 0;
}

static long syscall_hook_demo_exit(void *__user reserved)
{
    if(hook_type == FUNCTION_POINTER_CHAIN){
        fp_unhook_syscalln(__NR_openat, before_openat_1, 0);
        logkd("unhook __NR_openat");
    }
    return 0;
}

KPM_INIT(syscall_hook_demo_init);
KPM_CTL0(syscall_hook_control0);
KPM_EXIT(syscall_hook_demo_exit);

