#include "df_ipc.h"
#include "df_common.h"
#include "df_constants.h"
#include "anti_hook_detector.h"

struct user_data_node {
    struct user_data data;
    struct list_head list;
};

static LIST_HEAD(g_user_data_list);

// 查找指定uid的节点
static struct user_data_node* df_find_user_data_node(int uid) {
    struct user_data_node *pos;
    list_for_each_entry(pos, &g_user_data_list, list) {
        if (pos->data.uid == uid) return pos;
    }
    return NULL;
}

// 添加或更新user_data
static void df_user_data_list_add_or_update(const struct user_data *new_data) {
    if (!new_data) return;
    struct user_data_node *node = df_find_user_data_node(new_data->uid);
    if (node) {
        memcpy(&node->data, new_data, sizeof(struct user_data));
    } else {
        node = df_vmalloc(sizeof(struct user_data_node));
        if (!node) {
            logke("df_user_data_list_add_or_update, df_vmalloc failed");
            return;
        }
        memcpy(&node->data, new_data, sizeof(struct user_data));
        INIT_LIST_HEAD(&node->list);
        list_add_tail(&node->list, &g_user_data_list);
    }
}

// 按uid删除节点
static void df_user_data_list_delete_by_uid(int uid) {
    struct user_data_node *pos, *n;
    list_for_each_entry_safe(pos, n, &g_user_data_list, list) {
        if (pos->data.uid == uid) {
            list_del(&pos->list);
            df_vfree(pos);
            return;
        }
    }
}

// 清理全部
static void df_user_data_list_clear(void) {
    struct user_data_node *pos, *n;
    list_for_each_entry_safe(pos, n, &g_user_data_list, list) {
        list_del(&pos->list);
        df_vfree(pos);
    }
}

// 获取链表中元素个数
static int df_user_data_list_size(void) {
    struct user_data_node *pos;
    int count = 0;
    list_for_each_entry(pos, &g_user_data_list, list) {
        count++;
    }
    return count;
}



enum hook_type hook_type = NONE;

static void df_handle_submit_data(hook_fargs6_t *args, void *udata)
{
    int targetUid = (int)syscall_argn(args, 1);
    const struct user_data* userData = (struct user_data*)syscall_argn(args, 2);
    int userDataSize = (int)syscall_argn(args, 3);

    uid_t uid = current_uid();
    pid_t pid = df_current_pid();
    const struct user_data kUserData;
    logkd("df_handle_submit_data targetUid=%d sizeof(struct user_data)=%d", targetUid,sizeof(struct user_data));
    

    int ret = df_copy_from_user((void*)&kUserData, (void*)userData, userDataSize);
    if(ret == DF_FAILED){
        logke("df_handle_submit_data copy userdata failed");
        return;
    }

    logkd("total_running_time=%ld total_sleep_time=%lld", kUserData.total_running_time, kUserData.total_sleep_time);
    logkd("stat_random1=%d stat_random2=%d stat_random3=%d stat_random4=%d",kUserData.stat_random1, kUserData.stat_random2, kUserData.stat_random3, kUserData.stat_random4);

    df_user_data_list_add_or_update(&kUserData);
    logkd("after df_handle_submit_data list_size=%d", df_user_data_list_size());
    
    args->skip_origin = true;
    args->ret = 0;
}

static void df_handle_delete_data(hook_fargs6_t *args, void *udata)
{
    int targetUid = (int)syscall_argn(args, 1);
    const struct user_data* userData = (struct user_data*)syscall_argn(args, 2);
    int userDataSize = (int)syscall_argn(args, 3);
    logkd("df_handle_delete_data targetUid=%d", targetUid);

    uid_t uid = current_uid();
    pid_t pid = df_current_pid();
    const struct user_data kUserData;

    df_user_data_list_delete_by_uid(targetUid);
    logkd("after df_handle_delete_data list_size=%d", df_user_data_list_size());
    args->skip_origin = true;
    args->ret = 0;
}



static void dump_hook_info(const struct hook_info_data *info) {
    if (!info) return;

    char original_buf[FUNC_PATCH_LEN * 3 + 1];
    char hooked_buf[FUNC_PATCH_LEN * 3 + 1];
    char *p;
    int i;

    // 格式化原始代码
    // memset(original_buf, 0, sizeof(original_buf));
    p = original_buf;
    for (i = 0; i < FUNC_PATCH_LEN; ++i) {
        p += sprintf(p, "%02X ", info->original_code[i]);
    }

    // 格式化hook代码
    // memset(hooked_buf, 0, sizeof(hooked_buf));
    p = hooked_buf;
    for (i = 0; i < FUNC_PATCH_LEN; ++i) {
        p += sprintf(p, "%02X ", info->hooked_code[i]);
    }

    logkd("HookInfo: uid=%d pid=%d addr=%p page=0x%lx enabled=%d",
         info->uid, info->pid, info->func_addr, info->page_addr, info->enabled);
    logkd("  original: %s", original_buf);
    logkd("  hooked:   %s", hooked_buf);
}



static void df_handle_submit_hook_data(hook_fargs6_t *args, void *udata)
{
    const struct hook_info_data* userHookInfo = (struct hook_info_data*)syscall_argn(args, 1);
    int hookInfoLen = (int)syscall_argn(args, 2);
    struct hook_info_data kHookInfo;
    logkd("hookInfoLen=%d", hookInfoLen);


    int ret = df_copy_from_user((void*)&kHookInfo, (void*)userHookInfo, hookInfoLen);
    if(ret == DF_FAILED){
        logke("copy hookinfo failed");
        return;
    }

    dump_hook_info(&kHookInfo);
    process_hook_info(&kHookInfo);
    logkd("after process_hook_info");
    args->skip_origin = true;
    args->ret = 0;
}

static void df_before_custom_syscall(hook_fargs6_t *args, void *udata)
{
    int sockfd = (int)syscall_argn(args, 0);
    if(sockfd == ACTION_SUBMIT_DATA){
        df_handle_submit_data(args, udata);
        return;
    }else if (sockfd == ACTION_DELETE_DATA)
    {
        df_handle_delete_data(args, udata);
        return;
    }else if(sockfd == ACTION_SUBMIT_HOOK_DATA){
        logkd("df_before_custom_syscall ACTION_SUBMIT_HOOK_DATA");
        df_handle_submit_hook_data(args, udata);
        return;
    }
}


int df_ipc_init(const char *args, const char *event, void *__user reserved)
{
    hook_err_t err = HOOK_NO_ERR;
    err = fp_hook_syscalln(CUSTOM_SYSCALL_NO, 6, df_before_custom_syscall, 0, 0);
    if (err) {
        logkd("hook custom_syscall error: %d\n", err);
    } else {
        logkd("hook custom_syscall success\n");
        hook_type = FUNCTION_POINTER_CHAIN;
    }
    return 0;
}

int df_ipc_exit(void *__user reserved)
{
    if(hook_type == FUNCTION_POINTER_CHAIN){
        fp_unhook_syscalln(CUSTOM_SYSCALL_NO, df_before_custom_syscall, 0);
        logkd("unhook CUSTOM_SYSCALL_NO\n");
    }

    df_user_data_list_clear();
}

struct user_data* df_query_user_data(int uid)
{
    struct user_data_node* node = df_find_user_data_node(uid);
    if(node){
        return &node->data;
    }
    return NULL;
}