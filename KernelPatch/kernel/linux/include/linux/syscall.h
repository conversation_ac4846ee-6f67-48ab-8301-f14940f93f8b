#ifndef _LINUX_SYSCALLS_H
#define _LINUX_SYSCALLS_H

#include <ktypes.h>

struct iocb;
struct io_event;
struct __kernel_timespec;
struct __aio_sigset;
struct io_uring_params;
struct old_timespec32;
struct epoll_event;
struct statfs;
struct statfs64;
struct open_how;
struct linux_dirent64;
struct iovec;
struct pollfd;
struct stat;
struct stat64;
struct __kernel_itimerspec;
struct old_itimerspec32;
struct siginfo;
struct rusage;
struct robust_list_head;
struct futex_waitv;
struct __kernel_old_itimerval;
struct kexec_segment;
struct sigevent;
struct sched_param;
struct sigaltstack;
struct sigaction;
struct tms;
struct new_utsname;
struct rlimit;
struct getcpu_cache;
struct __kernel_old_timeval;
struct timezone;
struct __kernel_timex;
struct old_timex32;
struct sysinfo;
struct mq_attr;
struct msqid_ds;
struct msgbuf;
struct sembuf;
struct shmid_ds;
struct sockaddr;
struct user_msghdr;
struct clone_args;
struct perf_event_attr;
struct mmsghdr;
struct rlimit64;
struct file_handle;
struct sched_attr;
union bpf_attr;
typedef struct rwf_t rwf_t;
struct statx;
struct rseq;
struct mount_attr;
struct landlock_ruleset_attr;
enum landlock_rule_type;
struct utimbuf;
struct old_utimbuf32;
struct old_timeval32;
struct linux_dirent;
struct old_sigaction;
struct __old_kernel_stat;
struct sel_arg_struct;
struct old_linux_dirent;
struct old_utsname;
struct oldold_utsname;
struct mmap_arg_struct;
struct sembuf;
struct timespec64;
struct ipc_namespace;

#endif