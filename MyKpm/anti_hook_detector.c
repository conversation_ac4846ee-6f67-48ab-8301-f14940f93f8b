#include "anti_hook_detector.h"
#include "df_common.h"

// 常量定义
#ifndef GFP_KERNEL
#define GFP_KERNEL 0x360
#endif

#ifndef PAGE_SIZE
#define PAGE_SIZE 4096
#endif

#ifndef ENOMEM
#define ENOMEM 12
#endif

#ifndef EINVAL
#define EINVAL 22
#endif

#ifndef ENOENT
#define ENOENT 2
#endif

// hook条目链表节点
struct hook_entry_node {
    struct hook_entry data;
    struct list_head list;
};

// 全局hook链表和锁
static LIST_HEAD(g_hook_list);
static int g_hook_list_initialized = 0;

// 内核函数指针（安全版本）
static struct task_struct* (*find_task_by_vpid_fn)(pid_t pid) = NULL;
static int g_kernel_functions_initialized = 0;

// 函数前向声明
static int enable_anti_hook_detection(struct hook_entry *entry);
static int disable_anti_hook_detection(struct hook_entry *entry);
static int create_shadow_memory(struct hook_entry *entry);
static int setup_page_protection(struct hook_entry *entry);
static int restore_original_mapping(struct hook_entry *entry);
static struct hook_entry_node* find_hook_node_safe(unsigned int uid, unsigned long func_addr);
static int validate_hook_entry(const struct hook_entry *entry);

// 安全的内核函数初始化
static int init_kernel_functions_safe(void) {
    if (g_kernel_functions_initialized) {
        return 0; // 已初始化
    }

    logkd("Initializing kernel functions safely...");

    // 查找find_task_by_vpid函数
    find_task_by_vpid_fn = (void*)kallsyms_lookup_name("find_task_by_vpid");
    if (find_task_by_vpid_fn) {
        logkd("find_task_by_vpid found at %p", find_task_by_vpid_fn);
    } else {
        logkw("find_task_by_vpid not found, will use current process");
    }

    g_kernel_functions_initialized = 1;
    logkd("Kernel functions initialization completed");
    return 0;
}

// 验证hook条目的有效性
static int validate_hook_entry(const struct hook_entry *entry) {
    if (!entry) {
        logke("Hook entry is NULL");
        return -EINVAL;
    }

    if (!entry->func_addr) {
        logke("Invalid function address: 0x%lx", entry->func_addr);
        return -EINVAL;
    }

    if (!entry->page_addr) {
        logke("Invalid page address: 0x%lx", entry->page_addr);
        return -EINVAL;
    }

    // 检查地址对齐
    if (entry->func_addr & 0x3) {
        logkw("Function address not aligned: 0x%lx", entry->func_addr);
    }

    return 0;
}

// 安全查找指定uid和func_addr的hook节点
static struct hook_entry_node* find_hook_node_safe(unsigned int uid, unsigned long func_addr) {
    struct hook_entry_node *pos;

    if (!g_hook_list_initialized) {
        logkw("Hook list not initialized");
        return NULL;
    }

    // 遍历链表查找匹配的节点
    list_for_each_entry(pos, &g_hook_list, list) {
        if (pos && pos->data.uid == uid && pos->data.func_addr == func_addr) {
            return pos;
        }
    }
    return NULL;
}

// 安全地添加或更新hook节点
static int add_or_update_hook_node_safe(const struct hook_info_data *hook_info) {
    struct hook_entry_node *node;

    if (!hook_info) {
        logke("Invalid hook_info parameter");
        return -EINVAL;
    }

    // 初始化链表（如果需要）
    if (!g_hook_list_initialized) {
        INIT_LIST_HEAD(&g_hook_list);
        g_hook_list_initialized = 1;
        logkd("Hook list initialized");
    }

    // 查找现有节点
    node = find_hook_node_safe(hook_info->uid, (unsigned long)hook_info->func_addr);

    if (node) {
        // 更新现有节点
        logkd("Updating existing hook node for uid=%d, addr=0x%lx",
              hook_info->uid, (unsigned long)hook_info->func_addr);

        node->data.pid = hook_info->pid;
        node->data.page_addr = hook_info->page_addr;
        node->data.enabled = 0;  // 重置为未启用状态

        // 安全拷贝数组
        memcpy(node->data.original_code, hook_info->original_code, FUNC_PATCH_LEN);
        memcpy(node->data.hooked_code, hook_info->hooked_code, FUNC_PATCH_LEN);

    } else {
        // 创建新节点
        logkd("Creating new hook node for uid=%d, addr=0x%lx",
              hook_info->uid, (unsigned long)hook_info->func_addr);

        node = df_vmalloc(sizeof(struct hook_entry_node));
        if (!node) {
            logke("Failed to allocate memory for hook node");
            return -ENOMEM;
        }

        // 清零内存
        memset(node, 0, sizeof(struct hook_entry_node));

        // 初始化数据
        node->data.uid = hook_info->uid;
        node->data.pid = hook_info->pid;
        node->data.func_addr = (unsigned long)hook_info->func_addr;
        node->data.page_addr = hook_info->page_addr;
        node->data.shadow_vaddr = 0;  // 使用虚拟地址而不是struct page*
        node->data.enabled = 0;
        node->data.page_remapped = 0;

        // 安全拷贝数组
        memcpy(node->data.original_code, hook_info->original_code, FUNC_PATCH_LEN);
        memcpy(node->data.hooked_code, hook_info->hooked_code, FUNC_PATCH_LEN);

        // 初始化链表节点并添加到链表
        INIT_LIST_HEAD(&node->list);
        list_add_tail(&node->list, &g_hook_list);
    }

    return 0;
}

// 安全删除指定的hook节点
static void remove_hook_node_safe(struct hook_entry_node *node) {
    if (!node) {
        logkw("Attempting to remove NULL hook node");
        return;
    }

    logkd("Removing hook node: uid=%d, addr=0x%lx",
          node->data.uid, node->data.func_addr);

    // 先禁用反hook检测
    if (node->data.enabled) {
        disable_anti_hook_detection(&node->data);
    }

    // 从链表中删除
    list_del(&node->list);

    // 释放内存
    df_vfree(node);
}

// 按uid安全删除所有hook节点
static void remove_hooks_by_uid_safe(unsigned int uid) {
    struct hook_entry_node *pos, *n;
    int removed_count = 0;

    if (!g_hook_list_initialized) {
        logkw("Hook list not initialized");
        return;
    }

    logkd("Removing all hooks for uid=%d", uid);

    list_for_each_entry_safe(pos, n, &g_hook_list, list) {
        if (pos && pos->data.uid == uid) {
            remove_hook_node_safe(pos);
            removed_count++;
        }
    }

    logkd("Removed %d hooks for uid=%d", removed_count, uid);
}

// 获取hook数量
static int get_hook_list_size(void) {
    struct hook_entry_node *pos;
    int count = 0;
    list_for_each_entry(pos, &g_hook_list, list) {
        count++;
    }
    return count;
}

// 获取指定uid的hook数量
static int get_hook_count_by_uid(unsigned int uid) {
    struct hook_entry_node *pos;
    int count = 0;
    list_for_each_entry(pos, &g_hook_list, list) {
        if (pos->data.uid == uid) {
            count++;
        }
    }
    return count;
}

// 安全创建影子内存区域
static int create_shadow_memory_safe(struct hook_entry *entry) {
    unsigned long shadow_vaddr;
    unsigned long offset;

    if (validate_hook_entry(entry) != 0) {
        return -EINVAL;
    }

    logkd("Creating shadow memory for addr 0x%lx", entry->func_addr);

    // 检查是否已经创建过
    if (entry->shadow_vaddr != 0) {
        logkw("Shadow memory already exists for addr 0x%lx", entry->func_addr);
        return 0;
    }

    // 分配一个页面 (order=0 表示分配1个页面)
    shadow_vaddr = df__get_free_pages(GFP_KERNEL, 0);
    if (!shadow_vaddr || shadow_vaddr == (unsigned long)-1) {
        logke("Failed to allocate shadow page");
        return -ENOMEM;
    }

    logkd("Shadow page allocated at 0x%lx", shadow_vaddr);

    // 清零页面
    memset((void*)shadow_vaddr, 0, PAGE_SIZE);

    // 计算偏移并拷贝原始代码到影子页面
    offset = entry->func_addr & (PAGE_SIZE - 1);
    memcpy((void*)(shadow_vaddr + offset), entry->original_code, FUNC_PATCH_LEN);

    // 保存影子页面虚拟地址
    entry->shadow_vaddr = shadow_vaddr;

    logkd("Shadow memory created successfully:");
    logkd("  Shadow addr: 0x%lx, offset: 0x%lx", shadow_vaddr, offset);
    logkd("  Original code: %02X %02X %02X %02X",
          entry->original_code[0], entry->original_code[1],
          entry->original_code[2], entry->original_code[3]);

    return 0;
}

// 核心功能：设置页面保护以实现读取/执行分离
// 策略：保持hook代码在目标地址，通过页面错误处理来拦截读取访问
static int setup_page_protection(struct hook_entry *entry) {
    unsigned char *target_addr;
    unsigned char *shadow_addr;
    unsigned long offset;

    if (validate_hook_entry(entry) != 0) {
        return -EINVAL;
    }

    logkd("Setting up page protection for addr 0x%lx, pid=%d",
          entry->func_addr, entry->pid);

    // 检查影子页面是否已创建
    if (entry->shadow_vaddr == 0) {
        logke("Shadow page not created");
        return -EINVAL;
    }

    // 检查是否已经处理过
    if (entry->page_remapped) {
        logkd("Page protection already set for addr 0x%lx", entry->func_addr);
        return 0;
    }

    // 初始化内核函数（如果需要）
    if (init_kernel_functions_safe() != 0) {
        logkw("Failed to initialize kernel functions, continuing anyway");
    }

    // 获取目标地址指针
    target_addr = (unsigned char*)entry->func_addr;

    // 保存当前内容（应该是hook代码）
    memcpy(entry->current_code, target_addr, FUNC_PATCH_LEN);

    logkd("Current code at target 0x%lx: %02X %02X %02X %02X",
          entry->func_addr,
          target_addr[0], target_addr[1], target_addr[2], target_addr[3]);

    // 验证当前代码是否与保存的hook代码一致
    if (memcmp(entry->current_code, entry->hooked_code, FUNC_PATCH_LEN) != 0) {
        logkw("Current code doesn't match saved hooked code");
        logkw("Expected: %02X %02X %02X %02X",
              entry->hooked_code[0], entry->hooked_code[1],
              entry->hooked_code[2], entry->hooked_code[3]);
        // 更新保存的hook代码为当前实际代码
        memcpy(entry->hooked_code, entry->current_code, FUNC_PATCH_LEN);
    }

    // 在影子页面中保存hook代码（用于执行）
    shadow_addr = (unsigned char*)entry->shadow_vaddr;
    offset = entry->func_addr & (PAGE_SIZE - 1);

    // 将hook代码也保存到影子页面的对应位置
    memcpy(shadow_addr + offset, entry->hooked_code, FUNC_PATCH_LEN);

    logkd("Hook code saved to shadow page at offset 0x%lx", offset);

    // 核心策略：保持目标地址的hook代码不变，让CPU正常执行
    // 通过页面错误处理来拦截读取访问并返回原始代码

    // 标记为已处理
    entry->page_remapped = 1;

    logkd("Page protection setup completed successfully");
    logkd("  Target: addr=0x%lx (contains hook code for execution)", entry->func_addr);
    logkd("  Shadow: addr=0x%lx (contains both original and hook code)", entry->shadow_vaddr);
    logkd("  Strategy: Hook remains active, reads will be intercepted");

    return 0;
}

// 恢复原始内存映射
static int restore_original_mapping_safe(struct hook_entry *entry) {
    if (!entry) {
        logkw("Invalid entry for restore");
        return -EINVAL;
    }

    logkd("Restoring original mapping for addr 0x%lx", entry->func_addr);

    if (entry->page_remapped) {
        // 清理重映射状态
        entry->page_remapped = 0;
        logkd("Page remapping state cleared");
    }

    // 释放影子页面
    if (entry->shadow_vaddr != 0) {
        df_free_pages(entry->shadow_vaddr, 0);
        entry->shadow_vaddr = 0;
        logkd("Shadow page freed");
    }

    return 0;
}

// 启用反hook检测（安全版本）
static int enable_anti_hook_detection(struct hook_entry *entry) {
    int ret;

    if (!entry) {
        logke("Invalid entry for enable");
        return -EINVAL;
    }

    if (entry->enabled) {
        logkd("Anti-hook detection already enabled for addr 0x%lx", entry->func_addr);
        return 0;
    }

    logkd("Enabling anti-hook detection for uid %d, addr 0x%lx",
          entry->uid, entry->func_addr);

    // 创建影子内存
    ret = create_shadow_memory_safe(entry);
    if (ret != 0) {
        logke("Failed to create shadow memory: %d", ret);
        return ret;
    }

    // 设置页面保护
    ret = setup_page_protection(entry);
    if (ret != 0) {
        logke("Failed to setup page protection: %d", ret);
        // 清理已创建的影子内存
        restore_original_mapping_safe(entry);
        return ret;
    }

    entry->enabled = 1;
    logkd("Anti-hook detection enabled successfully");

    return 0;
}

// 禁用反hook检测
static int disable_anti_hook_detection(struct hook_entry *entry) {
    if (!entry) {
        logkw("Invalid entry for disable");
        return -EINVAL;
    }

    if (!entry->enabled) {
        logkd("Anti-hook detection already disabled for addr 0x%lx", entry->func_addr);
        return 0;
    }

    logkd("Disabling anti-hook detection for uid %d, addr 0x%lx",
          entry->uid, entry->func_addr);

    // 恢复原始内存映射
    restore_original_mapping_safe(entry);

    // 清理状态
    entry->enabled = 0;

    logkd("Anti-hook detection disabled successfully");
    return 0;
}

// 主要的处理函数 - 供外部调用（安全版本）
int process_hook_info(const struct hook_info_data *hook_info) {
    struct hook_entry_node *node;
    int ret = 0;

    // 参数验证
    if (!hook_info) {
        logke("Invalid hook_info parameter");
        return -EINVAL;
    }

    if (!hook_info->func_addr) {
        logke("Invalid function address");
        return -EINVAL;
    }

    logkd("Processing hook info: uid=%d, pid=%d, addr=%p, enabled=%d",
          hook_info->uid, hook_info->pid, hook_info->func_addr, hook_info->enabled);

    // 打印原始代码和hook代码用于调试
    logkd("Original code: %02X %02X %02X %02X",
          hook_info->original_code[0], hook_info->original_code[1],
          hook_info->original_code[2], hook_info->original_code[3]);
    logkd("Hooked code:   %02X %02X %02X %02X",
          hook_info->hooked_code[0], hook_info->hooked_code[1],
          hook_info->hooked_code[2], hook_info->hooked_code[3]);

    // 添加或更新hook节点
    ret = add_or_update_hook_node_safe(hook_info);
    if (ret != 0) {
        logke("Failed to add/update hook node: %d", ret);
        return ret;
    }

    // 查找刚添加/更新的节点
    node = find_hook_node_safe(hook_info->uid, (unsigned long)hook_info->func_addr);
    if (!node) {
        logke("Failed to find hook node after add/update");
        return -ENOENT;
    }

    logkd("Hook node processed successfully");

    // 根据enabled标志启用或禁用反hook检测
    if (hook_info->enabled) {
        logkd("Enabling anti-hook detection...");
        ret = enable_anti_hook_detection(&node->data);
        if (ret != 0) {
            logke("Failed to enable anti-hook detection: %d", ret);
            return ret;
        }
        logkd("Anti-hook detection enabled successfully");
    } else {
        logkd("Disabling anti-hook detection...");
        ret = disable_anti_hook_detection(&node->data);
        if (ret != 0) {
            logke("Failed to disable anti-hook detection: %d", ret);
            return ret;
        }
        logkd("Anti-hook detection disabled successfully");
    }

    logkd("Total hooks in list: %d", get_hook_list_size());
    return 0;
}

// 清理指定uid的所有hook信息
int cleanup_hooks_by_uid(unsigned int uid) {
    int count_before = get_hook_count_by_uid(uid);

    logkd("Cleaning up hooks for uid %d (count: %d)", uid, count_before);

    remove_hooks_by_uid_safe(uid);

    int count_after = get_hook_count_by_uid(uid);
    logkd("Cleaned up %d hooks for uid %d", count_before - count_after, uid);

    return 0;
}

// 核心功能：检查地址是否需要反hook处理
static struct hook_entry_node* find_hook_for_address(unsigned long addr) {
    struct hook_entry_node *pos;

    if (!g_hook_list_initialized) {
        return NULL;
    }

    list_for_each_entry(pos, &g_hook_list, list) {
        if (pos && pos->data.enabled &&
            addr >= pos->data.func_addr &&
            addr < pos->data.func_addr + FUNC_PATCH_LEN) {
            return pos;
        }
    }
    return NULL;
}

// 核心功能：模拟内存读取，返回原始代码
int anti_hook_memory_read(unsigned long addr, void *buffer, size_t size) {
    struct hook_entry_node *hook_node;
    unsigned long offset;
    size_t copy_size;

    if (!buffer || size == 0) {
        return -EINVAL;
    }

    // 查找是否有匹配的hook
    hook_node = find_hook_for_address(addr);
    if (!hook_node) {
        // 没有hook，直接读取原始内存
        memcpy(buffer, (void*)addr, size);
        return 0;
    }

    logkd("Anti-hook memory read at 0x%lx, size=%zu", addr, size);

    // 计算在函数内的偏移
    offset = addr - hook_node->data.func_addr;
    copy_size = (size > FUNC_PATCH_LEN - offset) ? (FUNC_PATCH_LEN - offset) : size;

    // 从原始代码中拷贝
    memcpy(buffer, hook_node->data.original_code + offset, copy_size);

    // 如果还有剩余数据，从实际内存读取
    if (size > copy_size) {
        memcpy((char*)buffer + copy_size,
               (void*)(addr + copy_size),
               size - copy_size);
    }

    logkd("Returned original code for read at 0x%lx", addr);
    return 0;
}

// 核心功能：内存读取拦截器
// 这个函数应该被集成到内存读取系统调用的处理中
int anti_hook_memory_read_interceptor(unsigned long addr, void *buffer, size_t size, int is_user_access) {
    struct hook_entry_node *hook_node;
    unsigned long offset;
    size_t copy_size;
    unsigned char *shadow_addr;

    if (!buffer || size == 0) {
        return -EINVAL;
    }

    // 查找是否有匹配的hook
    hook_node = find_hook_for_address(addr);
    if (!hook_node) {
        // 没有hook，直接读取原始内存
        return -1; // 表示不处理，让系统正常读取
    }

    // 只对用户空间的读取进行拦截（检测程序的读取）
    if (!is_user_access) {
        return -1; // 内核访问不拦截
    }

    logkd("Intercepting memory read at 0x%lx, size=%zu (returning original code)", addr, size);

    // 计算在函数内的偏移
    offset = addr - hook_node->data.func_addr;
    copy_size = (size > FUNC_PATCH_LEN - offset) ? (FUNC_PATCH_LEN - offset) : size;

    // 从影子页面的原始代码区域拷贝
    shadow_addr = (unsigned char*)hook_node->data.shadow_vaddr;
    unsigned long shadow_offset = hook_node->data.func_addr & (PAGE_SIZE - 1);

    // 影子页面中存储的是原始代码
    memcpy(buffer, shadow_addr + shadow_offset + offset, copy_size);

    // 如果还有剩余数据，从实际内存读取
    if (size > copy_size) {
        memcpy((char*)buffer + copy_size,
               (void*)(addr + copy_size),
               size - copy_size);
    }

    logkd("Returned original code for read at 0x%lx: %02X %02X %02X %02X",
          addr, ((unsigned char*)buffer)[0], ((unsigned char*)buffer)[1],
          ((unsigned char*)buffer)[2], ((unsigned char*)buffer)[3]);

    return 0; // 表示已处理
}

// 页面错误处理函数（简化版）
int anti_hook_page_fault_handler(struct pt_regs *regs, unsigned long addr, unsigned int esr) {
    struct hook_entry_node *hook_node;

    logkd("Page fault at addr 0x%lx, esr=0x%x", addr, esr);

    // 查找匹配的hook
    hook_node = find_hook_for_address(addr);
    if (!hook_node) {
        // 未找到匹配的hook，让其他处理器处理
        return -1;
    }

    logkd("Found matching hook for page fault: uid=%d, func_addr=0x%lx",
          hook_node->data.uid, hook_node->data.func_addr);

    // 在这个简化实现中，我们主要依赖内存读取拦截器
    // 页面错误处理比较复杂，需要更深入的内核知识
    logkd("Page fault handling - delegating to system (hook remains active)");

    return -1; // 让系统正常处理，保持hook功能
}

// 模块初始化（简化版）
int anti_hook_init(void) {
    logkd("Anti-hook detection module initialized (simple)");
    return 0;
}

// 模块清理
void anti_hook_cleanup(void) {
    struct hook_entry_node *pos, *n;
    int cleaned_count = 0;

    logkd("Cleaning up anti-hook detection module");

    if (!g_hook_list_initialized) {
        logkd("Hook list not initialized, nothing to clean");
        return;
    }

    // 清理所有hook节点
    list_for_each_entry_safe(pos, n, &g_hook_list, list) {
        remove_hook_node_safe(pos);
        cleaned_count++;
    }

    // 重置状态
    find_task_by_vpid_fn = NULL;
    g_kernel_functions_initialized = 0;
    g_hook_list_initialized = 0;

    logkd("Anti-hook detection module cleaned up, removed %d hooks", cleaned_count);
}

// 获取hook统计信息
int get_hook_count(unsigned int uid) {
    if (uid == 0) {
        // 返回所有hook数量
        return get_hook_list_size();
    } else {
        // 返回指定uid的hook数量
        return get_hook_count_by_uid(uid);
    }
}

// 启用指定进程的反hook检测
int enable_anti_hook_for_process(unsigned int uid, unsigned int pid, unsigned long func_addr) {
    struct hook_entry_node *node = find_hook_node_safe(uid, func_addr);
    if (!node) {
        logke("Hook not found for uid=%d, addr=0x%lx", uid, func_addr);
        return -ENOENT;
    }

    node->data.pid = pid; // 更新pid
    return enable_anti_hook_detection(&node->data);
}

// 禁用指定进程的反hook检测
int disable_anti_hook_for_process(unsigned int uid, unsigned int pid, unsigned long func_addr) {
    struct hook_entry_node *node = find_hook_node_safe(uid, func_addr);
    if (!node) {
        logke("Hook not found for uid=%d, addr=0x%lx", uid, func_addr);
        return -ENOENT;
    }

    return disable_anti_hook_detection(&node->data);
}

// 检查指定地址是否被hook并且启用了反检测
int is_address_anti_hooked(unsigned long addr) {
    struct hook_entry_node *hook_node = find_hook_for_address(addr);
    return (hook_node != NULL) ? 1 : 0;
}

// 获取hook信息用于调试
int get_hook_info(unsigned int uid, unsigned long func_addr, struct hook_info_data *info) {
    struct hook_entry_node *node = find_hook_node_safe(uid, func_addr);

    if (!node || !info) {
        return -EINVAL;
    }

    info->uid = node->data.uid;
    info->pid = node->data.pid;
    info->func_addr = (void*)node->data.func_addr;
    info->page_addr = node->data.page_addr;
    info->enabled = node->data.enabled;

    memcpy(info->original_code, node->data.original_code, FUNC_PATCH_LEN);
    memcpy(info->hooked_code, node->data.hooked_code, FUNC_PATCH_LEN);

    return 0;
}

// 调试函数：打印所有hook信息
void dump_all_hooks(void) {
    struct hook_entry_node *pos;
    int count = 0;

    if (!g_hook_list_initialized) {
        logkd("Hook list not initialized");
        return;
    }

    logkd("=== Hook Information Dump ===");
    logkd("Total hooks: %d", get_hook_list_size());

    list_for_each_entry(pos, &g_hook_list, list) {
        struct hook_entry *entry = &pos->data;
        logkd("Hook %d: uid=%d, pid=%d, addr=0x%lx, enabled=%d",
              count++, entry->uid, entry->pid, entry->func_addr, entry->enabled);

        // 打印部分原始代码和hook代码
        logkd("  original: %02X %02X %02X %02X...",
              entry->original_code[0], entry->original_code[1],
              entry->original_code[2], entry->original_code[3]);
        logkd("  hooked:   %02X %02X %02X %02X...",
              entry->hooked_code[0], entry->hooked_code[1],
              entry->hooked_code[2], entry->hooked_code[3]);
        logkd("  shadow:   0x%lx, remapped=%d",
              entry->shadow_vaddr, entry->page_remapped);
    }

    logkd("=== End Dump ===");
}