#include "df_common.h"
#include "df_constants.h"


struct pid_namespace;
pid_t (*p_task_pid_nr_ns)(struct task_struct *task, enum pid_type type, struct pid_namespace *ns) = 0;
int (*p_do_sys_open)(int dfd, const char __user *filename, int flags, umode_t mode) = 0;
int (*p_get_unused_fd_flags)(unsigned flags) = 0;
void (*p_put_unused_fd)(unsigned int fd) = 0;
void (*p_fd_install)(unsigned int fd, struct file *file) = 0;
struct file *(*p_filp_open)(const char *filename, int flags, umode_t mode) = 0;
int (*p_filp_close)(struct file *filp, fl_owner_t id) = 0;

void (*p_dump_stack)(void) = 0;
char *(*p__get_task_comm)(char *buf, size_t buf_size, struct task_struct *tsk) = 0;

long (*p_copy_from_user_nofault)(void *dst, const void __user *src, size_t size) = 0;
void (*p_vfree)(const void *addr) = 0;
void *(*p_vmalloc)(unsigned long size) = 0;
void (*p_ktime_get_real_ts64)(struct timespec64 *ts) = 0;
char *(*p_dentry_path)(struct dentry *dentry, char *buf, int buflen) = 0;
char *(*p_d_path)(const struct path *, char *, int) = 0;
void (*p_down_read)(struct rw_semaphore *sem) = 0;
void (*p_up_read)(struct rw_semaphore *sem) = 0;
struct mm_struct *(*p_get_task_mm)(struct task_struct *task) = 0;
void (*p_mmput)(struct mm_struct *) = 0;
unsigned long (*p___get_free_pages)(gfp_t gfp_mask, unsigned int order) = 0;
void (*p_free_pages)(unsigned long addr, unsigned int order) = 0;
struct file *(*p_fget)(unsigned int fd) = 0;
void (*p_fput)(struct file *) = 0;
int (*p___arm64_sys_close)(int fd) = 0;

int df_common_init(const char *args, const char *event, void *__user reserved)
{
    p_task_pid_nr_ns = (typeof(p_task_pid_nr_ns))kallsyms_lookup_name("__task_pid_nr_ns");
    p_do_sys_open = (typeof(p_do_sys_open))kallsyms_lookup_name("do_sys_open");
    p_get_unused_fd_flags = (typeof(p_get_unused_fd_flags))kallsyms_lookup_name("get_unused_fd_flags");
    p_put_unused_fd = (typeof(p_put_unused_fd))kallsyms_lookup_name("put_unused_fd");
    p_fd_install = (typeof(p_fd_install))kallsyms_lookup_name("fd_install");
    p_filp_open = (typeof(p_filp_open))kallsyms_lookup_name("filp_open");
    p_filp_close = (typeof(p_filp_close))kallsyms_lookup_name("filp_close");
    p_dump_stack = (typeof(p_dump_stack))kallsyms_lookup_name("dump_stack");
    p__get_task_comm = (typeof(p__get_task_comm))kallsyms_lookup_name("__get_task_comm");
    p_copy_from_user_nofault = (typeof(p_copy_from_user_nofault))kallsyms_lookup_name("copy_from_user_nofault");
    p_vfree = (typeof(p_vfree))kallsyms_lookup_name("vfree");
    p_vmalloc = (typeof(p_vmalloc))kallsyms_lookup_name("vmalloc");
    p_ktime_get_real_ts64 = (typeof(p_ktime_get_real_ts64))kallsyms_lookup_name("ktime_get_real_ts64");
    p_dentry_path = (typeof(p_dentry_path))kallsyms_lookup_name("dentry_path");
    p_d_path = (typeof(p_d_path))kallsyms_lookup_name("d_path");
    p_down_read = (typeof(p_down_read))kallsyms_lookup_name("down_read");
    p_up_read = (typeof(p_up_read))kallsyms_lookup_name("up_read");
    p_get_task_mm = (typeof(p_get_task_mm))kallsyms_lookup_name("get_task_mm");
    p_mmput = (typeof(p_mmput))kallsyms_lookup_name("mmput");
    p___get_free_pages = (typeof(p___get_free_pages))kallsyms_lookup_name("__get_free_pages");
    p_free_pages = (typeof(p_free_pages))kallsyms_lookup_name("free_pages");
    p_fget = (typeof(p_fget))kallsyms_lookup_name("fget");
    p_fput = (typeof(p_fput))kallsyms_lookup_name("fput");
    p___arm64_sys_close = (typeof(p___arm64_sys_close))kallsyms_lookup_name("__arm64_sys_close");

    if(!p_task_pid_nr_ns){
        logke("lookup __task_pid_nr_ns failed");
    }
    if(!p_do_sys_open){
        logke("lookup do_sys_open failed");
    }
    if(!p_get_unused_fd_flags){
        logke("lookup get_unused_fd_flags failed");
    }
    if(!p_put_unused_fd){
        logke("lookup put_unused_fd failed");
    }
    if(!p_fd_install){
        logke("lookup fd_install failed");
    }
    if(!p_filp_open){
        logke("lookup filp_open failed");
    }

    if(!p_filp_close){
        logke("lookup filp_close failed");
    }

    if(!p_dump_stack){
        logke("lookup dump_stack failed");
    }

    if(!p__get_task_comm){
        logke("lookup __get_task_comm failed");
    }

    if(!p_copy_from_user_nofault){
        logke("lookup copy_from_user_nofault failed");
    }

    if(!p_vfree){
        logke("lookup vfree failed");
    }

    if(!p_vmalloc){
        logke("lookup vmalloc failed");
    }

    if(!p_ktime_get_real_ts64){
        logke("lookup ktime_get_real_ts64 failed");
    }

    if(!p_dentry_path){
        logke("lookup dentry_path failed");
    }

    if(!p_d_path){
        logke("lookup d_path failed");
    }

    if(!p_down_read){
        logke("lookup down_read failed");
    }

    if(!p_up_read){
        logke("lookup up_read failed");
    }

    if(!p_get_task_mm){
        logke("lookup get_task_mm failed");
    }

    if(!p_mmput){
        logke("lookup mmput failed");
    }

    if(!p___get_free_pages){
        logke("lookup __get_free_pages failed");
    }

    if(!p_free_pages){
        logke("lookup free_pages failed");
    }
   
    if(!p_fget){
        logke("lookup fget failed");
    }

    if(!p_fput){
        logke("lookup fput failed");
    }

    if(!p___arm64_sys_close){
        logke("lookup __arm64_sys_close failed");
    }

    return DF_SUCCESS;
}

int df_common_exit(void *__user reserved)
{
    logkd("common_exit");
    return DF_SUCCESS;
}

pid_t df_current_pid()
{
    struct task_struct *task = current;
    if (p_task_pid_nr_ns) {
        return p_task_pid_nr_ns(task, PIDTYPE_PID, 0);
    }
    return DF_FAILED;
}

int df_do_sys_open(int dfd, const char __user *filename, int flags, umode_t mode)
{
    if(p_do_sys_open){
        // logkd("call df_do_sys_open...");
    }
}

int df_get_unused_fd_flags(unsigned flags)
{
    if(p_get_unused_fd_flags){
        // logkd("call df_get_unused_fd_flags...");
        return p_get_unused_fd_flags(flags);
    }
    return -1;
}

void df_put_unused_fd(unsigned int fd)
{
	if(p_put_unused_fd){
        p_put_unused_fd(fd);
    }
}


void df_fd_install(unsigned int fd, struct file *file)
{
    if(p_fd_install){
        // logkd("call df_fd_install...");
        p_fd_install(fd, file);
    }
}

struct file* df_filp_open(const char *filename, int flags, umode_t mode){
    if(p_filp_open){
        // logkd("call df_filp_open");
        return p_filp_open(filename, flags, mode);
    }
    return NULL;
}

int df_filp_close(struct file *filp, fl_owner_t id)
{
    if(p_filp_close){
        // logkd("call df_filp_close");
        return p_filp_close(filp, id);
    }
    return NULL;
}

void df_dump_stack(void)
{
    if(p_dump_stack){
        // logkd("call df_dump_stack");
        return p_dump_stack();
    }
}

char *df__get_task_comm(char *buf, size_t buf_size, struct task_struct *tsk)
{
    if(p__get_task_comm){
        // logkd("call df__get_task_comm");
        return p__get_task_comm(buf, buf_size, tsk);
    }
    return NULL;
}

long df_copy_from_user(void *dst, const void __user *src, size_t size)
{
    if(p_copy_from_user_nofault){
        return p_copy_from_user_nofault(dst, src, size);
    }
    return DF_FAILED;
}

void df_vfree(const void *addr)
{
    if(p_vfree){
        p_vfree(addr);
    }
}

void *df_vmalloc(unsigned long size)
{
    if(p_vmalloc){
        return p_vmalloc(size);
    }
    return NULL;
}

void df_ktime_get_real_ts64(struct timespec64 *ts)
{
    if(p_ktime_get_real_ts64){
        p_ktime_get_real_ts64(ts);
    }
}


bool df_starts_with(const char *str, const char *prefix) {
    if (!str || !prefix) return false;
    size_t len = strlen(prefix);
    return strncmp(str, prefix, len) == 0;
}

char *df_dentry_path(struct dentry *dentry, char *buf, int buflen)
{
    if(p_dentry_path){
        return p_dentry_path(dentry, buf, buflen);
    }
    return NULL;
}

char *df_d_path(const struct path * path, char * buf, int len)
{
    if(p_d_path){
        return p_d_path(path, buf, len);
    }
    return NULL;
}

void df_down_read(struct rw_semaphore *sem)
{
    if(p_down_read){
        p_down_read(sem);
    }
}
void df_up_read(struct rw_semaphore *sem)
{
    if(p_up_read){
        p_up_read(sem);
    }
}


struct mm_struct *df_get_task_mm(struct task_struct *task)
{
    if(p_get_task_mm){
        return p_get_task_mm(task);
    }
    return NULL;
}


void df_mmput(struct mm_struct * mm)
{
    if(p_mmput){
        p_mmput(mm);
    }
}


unsigned long df__get_free_pages(gfp_t gfp_mask, unsigned int order)
{
    if(p___get_free_pages){
        return p___get_free_pages(gfp_mask, order);
    }
    return -1;
}
void df_free_pages(unsigned long addr, unsigned int order)
{
    if(p_free_pages){
        p_free_pages(addr, order);
    }
}

struct file * df_fget(unsigned int fd){
    if(p_fget){
        return p_fget(fd);
    }
    return NULL;
}

void df_fput(struct file * file)
{
    if(p_fput){
        p_fput(file);
    }
}

static int GFP_KERNEL = 0x360;
static int PAGE_SIZE = 4096;


bool df_get_path_from_fd(int fd, char *outbuf, size_t buflen)
{
    struct file *file;
    char *tmp = NULL, *pathname = NULL;
    bool success = false;

    file = df_fget(fd);
    if (!file) return false;

    tmp = (char *)df__get_free_pages(GFP_KERNEL, 0);  // 0 = order 0 = 1 page
    if (!tmp) {
        df_fput(file);
        return false;
    }

    pathname = df_d_path(&file->f_path, tmp, PAGE_SIZE);
    if (!IS_ERR(pathname)) {
        snprintf(outbuf, buflen, "%s", pathname);
        success = true;
    }

    df_free_pages((unsigned long)tmp, 0);
    df_fput(file);

    return success;
}

int df___arm64_sys_close(int fd){
    if(p___arm64_sys_close){
        return p___arm64_sys_close(fd);
    }
    return -1;
}